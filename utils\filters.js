const ffmpeg = require("fluent-ffmpeg");

// **Audio filter presets**
const audioFilterMap = {
  bassboost: "equalizer=f=50:width_type=h:width=300:g=20",
  karaoke: "stereotools=mlev=0.03",
  trebleboost: "equalizer=f=3000:width_type=h:width=200:g=5",
  pitch: "asetrate=44100*1.2,aresample=44100",
  tempo: "atempo=1.5",
  slowtempo: "atempo=0.50",
  nightcore: "asetrate=44100*1.25,aresample=44100,atempo=1.25",
  vaporwave: "asetrate=44100*0.8,aresample=44100",
  reverb: "aecho=1.0:1.0:2000:0.8",
  flanger: "flanger",
  echo: "aecho=1.0:1.0:100:0.8",
  surround: "surround",
  phaser: "aphaser=in_gain=0.4",
  reverse: "areverse",
  distortion: "acrusher=bits=2:mix=1:mode=log:level_in=2.0:level_out=2.0",
  chipmunk: "asetrate=44100*1.5,aresample=44100",
  liverquad: "apulsator=hz=1",
  chorus: "chorus=0.5:0.9:50:0.4:0.25:2",
  lowpass: "lowpass=f=1000:width=200",
  stutter: "aloop=loop=1:size=44100",
  eq: "firequalizer=gain='if(lt(f,1000),20,0)'",
  destroy: "acrusher=bits=8:mix=0.5",
  earwax: "earwax",
  phaserfx: "aphaser=in_gain=0.4:out_gain=0.74:delay=3:decay=0.4:speed=0.5",
  softdist: "acrusher=level_in=2:bits=8:mode=log",
  lofi: "lowpass=f=1000,highpass=f=150,asetrate=32000,aresample=44100,acompressor=threshold=-20dB:ratio=4,afade=in:st=0:d=2",
  dying: "equalizer=f=100:t=h:width=200:g=6, equalizer=f=3000:t=h:width=200:g=4, aecho=0.8:0.9:100:0.3, chorus=0.5:0.7:50:0.4:0.3:0.5, flanger, bass=g=12, treble=g=8, stereotools=mlev=2, atempo=1.05, extrastereo=m=2, lowpass=f=3500, highpass=f=200, vibrato=f=6.5, tremolo=f=5:d=0.8"
};

// **Video filter presets - Fun visual effects**
const videoFilterMap = {
  // Color effects
  vintage: "curves=vintage",
  sepia: "colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131",
  blackwhite: "hue=s=0",
  negative: "negate",
  warm: "colortemperature=temperature=3000",
  cool: "colortemperature=temperature=7000",

  // Visual distortions
  blur: "boxblur=5:1",
  sharpen: "unsharp=5:5:1.0:5:5:0.0",
  pixelate: "scale=iw/10:ih/10:flags=neighbor,scale=iw*10:ih*10:flags=neighbor",
  mirror: "crop=iw/2:ih:0:0,split[left][tmp];[tmp]hflip[right];[left][right]hstack",
  fisheye: "lenscorrection=cx=0.5:cy=0.5:k1=-0.227:k2=-0.022",

  // Speed effects
  slowmo: "setpts=2.0*PTS", // 0.5x speed
  fastmo: "setpts=0.5*PTS", // 2x speed
  timelapse: "setpts=0.25*PTS", // 4x speed

  // Artistic effects
  cartoon: "edgedetect=low=0.1:high=0.4,negate[edges];[0:v][edges]overlay",
  emboss: "convolution='0 -1 0 -1 4 -1 0 -1 0:0 -1 0 -1 4 -1 0 -1 0:0 -1 0 -1 4 -1 0 -1 0:0 -1 0 -1 4 -1 0 -1 0'",
  oil: "convolution='1 1 1 1 1 1 1 1 1:1 1 1 1 1 1 1 1 1:1 1 1 1 1 1 1 1 1:1 1 1 1 1 1 1 1 1:1/9:1/9:1/9:1/9'",

  // Glitch effects
  glitch: "noise=alls=20:allf=t+u,hue=H=2*PI*t:s=sin(2*PI*t)+1",
  datamosh: "mpdecimate,setpts=N/FRAME_RATE/TB",
  rgb: "split=3[r][g][b];[r]lutrgb=g=0:b=0[r];[g]lutrgb=r=0:b=0[g];[b]lutrgb=r=0:g=0[b];[r][g]blend=all_mode=screen[rg];[rg][b]blend=all_mode=screen",

  // Fun effects
  rainbow: "hue=H=2*PI*t:s=sin(2*PI*t)+1",
  wave: "format=yuv444p,geq=lum='p(X,Y+10*sin(X/10))'",
  zoom: "zoompan=z='zoom+0.002':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=1",
  rotate: "rotate=PI*t/30",
  shake: "crop=in_w-60:in_h-60:30+30*sin(n*2*PI/25):30+30*cos(n*2*PI/25)",

  // Retro effects
  vhs: "noise=alls=10:allf=t,hue=s=0.9,curves=vintage",
  crt: "scale=640:480,scale=1920:1080:flags=neighbor",
  gameboy: "scale=160:144,scale=1920:1080:flags=neighbor,colorchannelmixer=.3:.4:.3:0:.3:.4:.3:0:.3:.4:.3",

  // Transition effects
  fade: "fade=in:0:30",
  wipe: "crop=iw*t/10:ih:0:0"
};

/**
 * **Apply audio filters to audio files using FFmpeg**
 * @param {string} inputPath - Path to input file (original file)
 * @param {string} outputPath - Path to output file (filtered file)
 * @param {string[]} filters - Array of filters to apply
 * @returns {Promise<void>}
 */
const applyAudioFilters = (inputPath, outputPath, filters) => {
  return new Promise((resolve, reject) => {
    const command = ffmpeg(inputPath);

    // Convert preset and custom filters to FFmpeg filter strings
    const ffmpegFilters = filters.map((filter) => audioFilterMap[filter] || filter).filter(Boolean);

    if (ffmpegFilters.length > 0) {
      command.audioFilters(ffmpegFilters);
    }

    command
      .save(outputPath)
      .on("end", resolve)
      .on("error", reject);
  });
};

/**
 * **Apply video filters to video files using FFmpeg**
 * @param {string} inputPath - Path to input file (original file)
 * @param {string} outputPath - Path to output file (filtered file)
 * @param {string[]} filters - Array of filters to apply
 * @returns {Promise<void>}
 */
const applyVideoFilters = (inputPath, outputPath, filters) => {
  return new Promise((resolve, reject) => {
    const command = ffmpeg(inputPath);

    // Convert preset and custom filters to FFmpeg filter strings
    const ffmpegFilters = filters.map((filter) => videoFilterMap[filter] || filter).filter(Boolean);

    if (ffmpegFilters.length > 0) {
      command.videoFilters(ffmpegFilters);
    }

    command
      .save(outputPath)
      .on("end", resolve)
      .on("error", reject);
  });
};

// Legacy function for backward compatibility
const applyFilters = applyAudioFilters;

module.exports = {
  applyFilters,
  applyAudioFilters,
  applyVideoFilters,
  filterMap: audioFilterMap, // Legacy export
  audioFilterMap,
  videoFilterMap
};
