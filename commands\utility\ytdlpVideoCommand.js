const YTDlpWrap = require("yt-dlp-wrap").default;
const ytDlpWrap = new YTDlpWrap("./bin/yt-dlp.exe");
const fs = require("fs");
const path = require("path");
const ffmpeg = require("fluent-ffmpeg");
const { applyFilters, filterMap } = require("../../utils/filters");
const { formatDuration } = require("../../utils/utils");
const { generateSecureUrl } = require("../../utils/urlSigner");

module.exports = {
  command: "mp4",
  aliases: ["ytv"],
  category: "utility",
  description: "Download video from links with optional filters",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const input = event.message.text.split(" ");
    const videoUrl = input[1];
    const filterInput = input.slice(2).join(" ");

    const maxFileSize = 200 * 1024 * 1024; // 200MB
    const supportedFilters = filterMap ? Object.keys(filterMap) : [];
    if (!videoUrl) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Please provide a valid video URL.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    } else if (!videoUrl.startsWith("https://")) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Invalid URL. Please provide a valid video URL.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // **Parsing Filters**
    let filters = [];
    let customFilters = [];

    if (filterInput) {
      const matches = filterInput.match(/\[([^\]]+)\]/); // Extract text within []
      if (matches) {
        const rawFilters = matches[1].split(","); // Split by comma
        rawFilters.forEach((filter) => {
          if (filter.startsWith("custom=")) {
            customFilters.push(filter.replace("custom=", "")); // Store custom filters
          } else if (supportedFilters.includes(filter)) {
            filters.push(filter); // Store preset filters
          }
        });
      }
    }

    const videoId = extractVideoId(videoUrl);
    // Fetch metadata for the video
    let metadata;
    try {
      metadata = await ytDlpWrap.getVideoInfo(videoUrl);

      if (metadata.is_live) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "You cannot download live videos.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      if (metadata.availability === "private") {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "This video is private and cannot be downloaded.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }
    } catch (error) {
      console.error("Failed to fetch video metadata:", error);
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Failed to fetch video metadata. Please try again later.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
    let user;

    if (event.source.type === "group") {
      // If event is from a group, use getGroupMemberProfile
      user = await client.getGroupMemberProfile(
        event.source.groupId,
        event.source.userId
      );
    } else {
      // Otherwise, use the standard getProfile for individual chat
      user = await client.getProfile(event.source.userId);
    }

    // Send initial confirmation message to the user
    let userProfile = user.displayName;
    // client.replyMessage(event.replyToken, {
    //   type: "text",
    //   text: "Downloading your video...",
    //   quoteToken: event.message.quoteToken,
    // });

    // Ensure the ./downloads directory exists
    const downloadsDir = "./static/downloads";
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir);
    }

    // Generate a timestamped filename
    const timestamp = Date.now();
    const originalFilePath = path.join(downloadsDir, `${timestamp}_original.mp4`);
    const filteredFilePath = path.join(downloadsDir, `${timestamp}_filtered.mp4`);

    ytDlpWrap
      .exec([
        videoUrl, // Use the provided video URL
        "-f",
        "best",
        "-o",
        originalFilePath, // Save the video in the downloads directory
      ])
      
      .on("ytDlpEvent", (eventType, eventData) =>
        console.log(eventType, eventData)
      )
      .on("error", (error) => console.error(error))
      .on("close", async () => {
        let finalFilePath = originalFilePath;
        let duration = metadata.duration * 1000;

        if (filters.length > 0 || customFilters.length > 0) {
          try {
            await applyFilters(originalFilePath, filteredFilePath, [...filters, ...customFilters]);
            finalFilePath = filteredFilePath;
            duration = await new Promise((resolve, reject) => {
              ffmpeg.ffprobe(filteredFilePath, (err, data) =>
                err ? reject(err) : resolve(data.format.duration * 1000)
              );
            });
          } catch (error) {
            return client.replyMessage({
              replyToken: event.replyToken,
              messages: [
                {
                  type: "text",
                  text: "Failed to apply filters.",
                  quoteToken: event.message.quoteToken,
                },
              ],
            });
          }
        }

        if (fs.statSync(finalFilePath).size > maxFileSize) {
          fs.unlinkSync(finalFilePath);
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "text",
                text: "Video size exceeds the maximum allowed size of 200 MB.",
                quoteToken: event.message.quoteToken,
              },
            ],
          });
        }
        // Determine the preview image URL
        let previewImageUrl = "";

        if (videoId) {
          // If video is from YouTube, use YouTube thumbnail
          previewImageUrl = `https://to-jpg.vercel.app/convert?url=${metadata.thumbnail}&format=jpg`;
        } else if (metadata.thumbnail) {
          // Use metadata.thumbnail if available
          previewImageUrl = metadata.thumbnail;
        } else if (
          metadata.thumbnails &&
          metadata.thumbnails[0] &&
          metadata.thumbnails[0].url
        ) {
          // Use the first thumbnail in metadata.thumbnails array if available
          previewImageUrl = metadata.thumbnails[0].url;
        } else {
          // Fallback to a default image if no thumbnail is found
          previewImageUrl = generateSecureUrl("/image/line.png");
        }

        // Generate secure URL for the video file
        const videoUrl = generateSecureUrl(`/downloads/${path.basename(finalFilePath)}`, 120); // 2 hours expiry

        // Send the downloaded video to the user with the thumbnail
        const messageContent = [
          {
            type: "video",
            originalContentUrl: videoUrl, // Use secure URL
            previewImageUrl: previewImageUrl, // Use dynamic thumbnail
          },
          {
            type: "text",
            text: `📼 ${metadata.title}\nFilters: ${
              [...filters, ...customFilters].join(", ") || "Normal"
            }\n${formatDuration(
              metadata.duration
            )}\n\nRequested by ${userProfile}`,
            quoteToken: event.message.quoteToken,
          },
        ];
        client.replyMessage({
          replyToken: event.replyToken,
          messages: messageContent,
        });

        if (filters.length > 0 || customFilters.length > 0) {
          fs.unlinkSync(originalFilePath);
        }
      });
  },
};

// Function to extract YouTube video ID from a URL
function extractVideoId(url) {
  const videoIdMatch = url.match(
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})|(?:https?:\/\/)?youtu\.be\/([a-zA-Z0-9_-]{11})/
  );
  return videoIdMatch ? videoIdMatch[1] || videoIdMatch[2] : null;
}
